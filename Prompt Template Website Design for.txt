Prompt Template: Website Design for "The Moon Event Center" (Richardson, TX)

Goal:Design a stunning, modern, and responsive website for "The Moon Event Center" located in Richardson, Texas. The site should reflect elegance, professionalism, and a sense of celebration. It must be optimized for both desktop and mobile, and should appeal to clients booking weddings, corporate events, parties, and community gatherings.

1. Project Overview:

Design a website for the Moon Event Center that:

Attracts clients seeking upscale yet versatile event space

Reflects a celestial/moonlit theme subtly through design, color, and layout

Is easy to navigate, visually engaging, and conversion-oriented

2. Target Audience:

Brides and grooms

Corporate event planners

Families planning parties (quinceañeras, birthdays, anniversaries)

Nonprofits/community orgs

3. Design Style:

Visual Tone: Elegant, Mystical, SophisticatedColors: Deep navy, silver, soft white accents (moonlight-inspired)Fonts: Modern serif for headers, clean sans-serif for bodyImagery: Night skies, soft lighting, elegant table setups, dance floorsAnimations: Smooth scroll, subtle transitions, maybe a parallax moon effect

4. Page Structure:

Homepage – Hero image/video of event center, CTA, intro blurb, featured events

About Us – The story behind the Moon Event Center, mission, location

Gallery – High-quality photos categorized by event type

Services / Packages – Detailed breakdown of packages, amenities

Calendar / Availability – Option to view open dates or request booking

Contact Us – Map, form, phone/email, embedded Google Map

Testimonials – Carousel or grid of client reviews

FAQ – Answers to common booking/event questions

5. Functionality / Tech Requirements:

Mobile-first responsive layout

Booking request form with calendar integration

SEO-optimized structure and metadata

Accessibility (WCAG 2.1 compliant)

Fast loading speeds

CMS for easy updates (WordPress, Webflow, or custom?)

6. Inspiration / Moodboard Notes:

(Each team member can drop links to sites with elements they like – layout, fonts, colors, transitions, etc.)

7. Must-Haves:

Clear CTA on every page ("Book a Tour", "Check Availability")

Event-type specific galleries

Social media integration (Instagram feed, maybe TikTok snippets)

Embedded map and parking info

Flexible backend for staff to update pricing/images

8. Tools / Tech Stack (Suggested):

Frontend: React or WebflowBackend: Lightweight CMS (WordPress or Sanity)Forms: Typeform, Tally, or custom with email automationCalendar: Google Calendar or Calendly integrationAnalytics: Google Analytics, Meta PixelSEO: Yoast, schema.org tags

9. Collaboration + Workflow Notes:

Assign parts of prompt to team leads (Design, Copy, Dev, etc.)

Drop questions/comments in Figma or Notion doc

Use this template as a base and evolve it with team input